page {
  /* 禁用点击高亮 */
  -webkit-tap-highlight-color: rgba(0,0,0,0);
}

/* 数据管理页面样式 */
.data-management-container {
  min-height: 100vh;
  background-color: #F5F7FB;
  padding: 36rpx;
}

/* 页面头部 */
.page-header {
  padding: 24rpx;
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #999;
}

/* 使用提示 */
.usage-tip {
  display: flex;
  align-items: flex-start;
  background: rgba(102, 234, 133, 0.15);
  border: 1rpx solid rgba(131, 234, 102, 0.2);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #718096;
  line-height: 1.4;
}

.tip-link {
  color: #06b6d4;
  text-decoration: underline;
}

/* 通用区域样式 */
.section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 16rpx;
  padding-left: 8rpx;
}

/* 数据概览卡片 */
.data-overview-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  padding: 32rpx;
}

.data-comparison {
  margin-bottom: 12rpx;
}

.comparison-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.comparison-header .data-label {
  flex: 1;
  font-size: 26rpx;
  font-weight: 600;
  color: #4a5568;
}

.local-header,
.cloud-header {
  width: 120rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #06b6d4;
}

.comparison-row {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.comparison-row:last-child {
  border-bottom: none;
}

.data-label {
  flex: 1;
  font-size: 28rpx;
  color: #2d3748;
}

.local-value,
.cloud-value {
  width: 120rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #4a5568;
}

.last-modified-info {
  padding-top: 16rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.local-time,
.cloud-time {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.cloud-time {
  margin-bottom: 0;
}

.time-label {
  font-size: 24rpx;
  color: #718096;
  margin-right: 8rpx;
}

.time-value {
  font-size: 24rpx;
  color: #4a5568;
  font-weight: 500;
}

/* 操作卡片通用样式 */
.data-overview-card,
.data-operations-card,
.maintenance-operations-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  padding: 24rpx;
}

/* 按钮通用样式 */
.operation-button,
.maintenance-button {
  display: flex;
  align-items: center;
  width: 100%;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  background: transparent;
}

.operation-button:last-child,
.maintenance-button:last-child {
  margin-bottom: 0;
}

.operation-button:active,
.maintenance-button:active {
  transform: scale(0.98);
}

/* 上传按钮样式 */
.upload-button.enabled {
  background: linear-gradient(135deg, #48bb78, #38a169);
  border-color: #38a169;
  color: white;
}

.upload-button.enabled .button-title {
  color: white;
}

.upload-button.enabled .button-desc {
  color: rgba(255, 255, 255, 0.8);
}

.upload-button.enabled:active {
  background: linear-gradient(135deg, #38a169, #2f855a);
}

.upload-button.disabled {
  background: #f7fafc !important;
  border-color: #e2e8f0 !important;
  opacity: 0.6;
}

/* 历史数据按钮样式 */
.history-button {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  border-color: #dd6b20;
  color: white;
}

.history-button .button-title {
  color: white;
}

.history-button .button-desc {
  color: rgba(255, 255, 255, 0.8);
}

.history-button:active {
  background: linear-gradient(135deg, #dd6b20, #c05621);
}

/* 维护按钮样式 */
.maintenance-button {
  background: transparent;
  border-color: #e2e8f0;
}

.maintenance-button:active {
  background: rgba(0, 0, 0, 0.02);
}

.maintenance-button.danger-dark {
  background: #e53e3e;
  border-color: #c53030;
  color: white;
}

.maintenance-button.danger-dark .button-title {
  color: white;
}

.maintenance-button.danger-dark .button-desc {
  color: rgba(255, 255, 255, 0.8);
}

.maintenance-button.danger-dark:active {
  background: #c53030;
}

.button-icon {
  font-size: 40rpx;
  margin: 0 0 0 24rpx;
  flex-shrink: 0;
}

.button-text {
  flex: 1;
}

.button-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2d3748;
}

.maintenance-button.danger .button-title {
  color: #e53e3e;
}

.button-desc {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.4;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e2e8f0;
  border-top: 4rpx solid #06b6d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #4a5568;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-overlay.show .modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.modal-content.danger-modal {
  border: 2rpx solid #fed7d7;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #E5E7EB;
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-button {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
}

.modal-button.cancel {
  background: #f7fafc;
  color: #4a5568;
}

.modal-button.confirm {
  background: #06b6d4;
  color: white;
}

.modal-button.danger {
  background: #e53e3e;
  color: white;
}

.modal-button:disabled {
  opacity: 0.5;
}

.modal-button:active {
  transform: scale(0.98);
}

/* 历史数据列表 */
.history-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.history-item {
  padding: 20rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-item.selected {
  border-color: #06b6d4;
  background: rgba(6, 182, 212, 0.1);
}

.history-item:active {
  transform: scale(0.98);
}

.history-date {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4rpx;
}

.history-time {
  font-size: 24rpx;
  color: #718096;
  margin-bottom: 8rpx;
}

.history-stats {
  font-size: 24rpx;
  color: #4a5568;
}

/* 清空确认相关样式 */
.warning-text {
  font-size: 28rpx;
  color: #e53e3e;
  margin-bottom: 16rpx;
  font-weight: 600;
}

.confirm-input-section {
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding-top: 24rpx;
}

.confirm-label {
  font-size: 26rpx;
  color: #4a5568;
  margin-bottom: 8rpx;
}

.confirm-target-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #e53e3e;
  background: rgba(254, 215, 215, 0.3);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.confirm-input {
  width: auto;
  height: 80rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #2d3748;
}

.confirm-input:focus {
  border-color: #06b6d4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .data-management-container {
    padding: 24rpx;
  }

  .page-header {
    padding: 30rpx 0 20rpx;
  }

  .page-title {
    font-size: 32rpx;
  }

  .page-subtitle {
    font-size: 24rpx;
  }

  .data-overview-card,
  .data-operations-card,
  .maintenance-operations-card {
    padding: 20rpx;
  }

  .usage-tip {
    padding: 20rpx;
    margin-bottom: 24rpx;
  }

  .tip-icon {
    font-size: 28rpx;
    margin-right: 12rpx;
  }

  .tip-title {
    font-size: 26rpx;
  }

  .tip-text {
    font-size: 24rpx;
  }

  .modal-content {
    margin: 20rpx;
    max-width: calc(100vw - 40rpx);
  }

  .comparison-header .data-label,
  .local-header,
  .cloud-header {
    font-size: 22rpx;
  }

  .local-value,
  .cloud-value {
    width: 100rpx;
    font-size: 26rpx;
  }

  .button-title {
    font-size: 28rpx;
  }

  .button-desc {
    font-size: 22rpx;
  }
}


