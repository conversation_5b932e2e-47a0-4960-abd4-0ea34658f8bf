/* 日收入计算器组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 36rpx;
  width: 90%;
  max-width: 800rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #E5E7EB;
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.85);
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.modal-close:active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transform: scale(0.95);
}

/* 模态框主体 */
.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 计算器描述 */
.calculator-description {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #06b6d4;
}

.description-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 输入组布局 */
.input-group-flex {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-with-unit {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0 12px;
  transition: border-color 0.2s ease;
}

.input-with-unit:focus-within {
  border-color: #06b6d4;
  background-color: #fff;
}

.number-input {
  flex: 1;
  height: 44px;
  font-size: 16px;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.input-unit {
  font-size: 14px;
  color: #666;
  margin-left: 8px;
}

/* 计算结果 */
.calculation-result {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  color: white;
}

.result-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.result-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.result-number {
  font-size: 28px;
  font-weight: 600;
}

.result-unit {
  font-size: 16px;
  opacity: 0.9;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.modal-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: #333;
}

.btn-primary {
  background: linear-gradient(135deg, #06b6d4 0%, #10b981 100%);
  color: white;
  border: 1px solid #06b6d4;
}

.btn-primary:hover {
  background-color: #0ea5b3;
  border-color: #0ea5b3;
}
